@tailwind base;
@tailwind components;
@tailwind utilities;

/* Definition of the design system. All colors, gradients, fonts, etc should be defined here. */

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 308 30% 59%;
    --primary-foreground: 0 0% 100%;

    --secondary: 220 100% 95%;
    --secondary-foreground: 308 30% 59%;

    --muted: 220 100% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 308 50% 95%;
    --accent-foreground: 308 30% 59%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;

    --radius: 0.5rem;

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: 240 5.3% 26.1%;

    --sidebar-primary: 308 30% 59%;

    --sidebar-primary-foreground: 0 0% 98%;

    --sidebar-accent: 308 50% 95%;

    --sidebar-accent-foreground: 308 30% 59%;

    --sidebar-border: 220 13% 91%;

    --sidebar-ring: 308 30% 70%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 308 30% 70%;
    --primary-foreground: 0 0% 100%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 308 30% 80%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 308 20% 25%;
    --accent-foreground: 308 30% 80%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 308 30% 70%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 308 20% 25%;
    --sidebar-accent-foreground: 308 30% 80%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 308 30% 80%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }
}

@layer utilities {
  /* Custom scrollbar styles */
  .scrollbar-thin {
    scrollbar-width: thin;
    scrollbar-color: rgb(209 213 219) rgb(243 244 246);
  }

  .scrollbar-thin::-webkit-scrollbar {
    width: 6px;
  }

  .scrollbar-thin::-webkit-scrollbar-track {
    background: rgb(243 244 246);
    border-radius: 3px;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb {
    background: rgb(209 213 219);
    border-radius: 3px;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb:hover {
    background: rgb(156 163 175);
  }

  /* Hide scrollbar for main content when not needed */
  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }

  /* Prevent horizontal overflow in tables and content */
  .table-container {
    overflow-x: auto;
    max-width: 100%;
  }

  /* Fix double scroll issue - ensure only main element scrolls */
  html, body {
    overflow: hidden !important;
    height: 100vh !important;
  }

  /* Ensure the root div takes full height */
  #root {
    height: 100vh !important;
    overflow: hidden !important;
  }

  /* Ensure content doesn't break layout */
  .content-container {
    min-width: 0;
    word-wrap: break-word;
    overflow-wrap: break-word;
  }

  /* Fix for long text in table cells */
  .table-cell-content {
    max-width: 200px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  /* Estilos para ícones da barra lateral - respeitando o design system */

  /* Ícones padrão na barra lateral */
  nav svg {
    color: #4b5563; /* text-gray-600 */
    stroke: #4b5563;
  }

  /* Ícones padrão no modo escuro */
  .dark nav svg {
    color: #9ca3af; /* text-gray-400 */
    stroke: #9ca3af;
  }

  /* Ícones ativos na barra lateral expandida (fundo lilac, ícone branco para contraste) */
  nav a[class*="bg-gradient-to-r"] svg,
  nav a[class*="from-twins-primary"] svg {
    color: white !important;
    stroke: white !important;
  }

  /* Ícones ativos na barra lateral colapsada (usando aria-current e active class) */
  /* Só aplica quando não há fundo gradiente (ou seja, na sidebar colapsada) */
  nav a[aria-current="page"]:not([class*="bg-gradient-to-r"]) svg,
  nav a.active:not([class*="bg-gradient-to-r"]) svg {
    color: #a84897 !important; /* twins-primary lilac */
    stroke: #a84897 !important;
  }

  /* Ícones ativos na barra lateral colapsada no modo escuro */
  .dark nav a[aria-current="page"]:not([class*="bg-gradient-to-r"]) svg,
  .dark nav a.active:not([class*="bg-gradient-to-r"]) svg {
    color: #c084b5 !important; /* twins-primary-dark */
    stroke: #c084b5 !important;
  }

  /* Efeito hover para ícones não ativos */
  nav a:hover:not([class*="bg-gradient-to-r"]):not([class*="bg-white"][class*="text-twins-primary"]) svg {
    color: #a84897 !important; /* twins-primary */
    stroke: #a84897 !important;
  }

  /* Efeito hover no modo escuro para ícones não ativos */
  .dark nav a:hover:not([class*="bg-gradient-to-r"]):not([class*="bg-gray-800"][class*="text-twins-primary"]) svg {
    color: #c084b5 !important; /* twins-primary-dark */
    stroke: #c084b5 !important;
  }

  /* Mobile Navigation Styles */
  @media (max-width: 767px) {
    /* Ensure mobile menu has proper touch targets */
    .mobile-nav-item {
      min-height: 44px;
      padding: 12px 16px;
    }

    /* Smooth transitions for mobile menu */
    .mobile-menu-overlay {
      backdrop-filter: blur(2px);
    }

    /* Prevent body scroll when mobile menu is open */
    .mobile-menu-open {
      overflow: hidden;
    }

    /* Enhanced touch feedback for mobile buttons */
    .mobile-touch-target {
      min-width: 44px;
      min-height: 44px;
    }

    /* Mobile-specific spacing */
    .mobile-content {
      padding: 16px;
    }
  }

  /* Accessibility improvements */
  @media (prefers-reduced-motion: reduce) {
    .mobile-menu-transition {
      transition: none;
    }
  }

  /* Focus styles for better accessibility */
  .mobile-nav-item:focus-visible {
    outline: 2px solid #a84897;
    outline-offset: 2px;
  }
}